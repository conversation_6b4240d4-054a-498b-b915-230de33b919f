{"Version": "2012-10-17", "Statement": [{"Sid": "AllowPolicyIam", "Effect": "Allow", "Action": ["iam:CreateRole", "iam:TagRole", "iam:PassRole", "iam:DeleteRole", "iam:DeleteRolePolicy", "iam:PutRolePolicy", "iam:AttachRolePolicy", "iam:UntagRole", "iam:DetachRolePolicy", "iam:CreateServiceLinkedRole"], "Resource": "*"}, {"Sid": "AllowPolicyLambda", "Effect": "Allow", "Action": ["lambda:CreateFunction", "lambda:UpdateFunctionCode", "lambda:UpdateFunctionConfiguration", "lambda:DeleteFunction", "lambda:InvokeFunction", "lambda:PublishLayerVersion", "lambda:DeleteLayerVersion", "lambda:AddPermission", "lambda:RemovePermission", "lambda:TagResource", "lambda:UntagResource", "lambda:PublishVersion", "lambda:<PERSON><PERSON><PERSON><PERSON><PERSON>", "lambda:<PERSON>ete<PERSON><PERSON><PERSON>", "lambda:<PERSON><PERSON><PERSON><PERSON><PERSON>", "lambda:PutFunctionEventInvokeConfig", "lambda:UpdateFunctionEventInvokeConfig", "lambda:DeleteFunctionEventInvokeConfig", "lambda:AddLayerVersionPermission", "lambda:RemoveLayerVersionPermission", "lambda:EnableReplication"], "Resource": "*"}, {"Sid": "AllowPolicyApiGateway", "Effect": "Allow", "Action": ["apigateway:GET", "apigateway:POST", "apigateway:PUT", "apigateway:DELETE", "apigateway:PATCH", "apigateway:UpdateRestApiPolicy"], "Resource": "*"}, {"Sid": "AllowPolicyS3", "Effect": "Allow", "Action": ["s3:CreateBucket", "s3:PutObject", "s3:DeleteObject", "s3:DeleteBucket", "s3:DeleteBucketPolicy", "s3:DeleteBucketWebsite", "s3:PutBucketLogging", "s3:PutBucketVersioning", "s3:PutBucketVersioning", "s3:PutAccelerateConfiguration", "s3:PutBucketOwnershipControls", "s3:PutLifecycleConfiguration", "s3:PutBucketCORS", "s3:PutBucketPolicy", "s3:CreateAccessPoint", "s3:PutBucketObjectLockConfiguration", "s3:PutBucketRequestPayment", "s3:PutInventoryConfiguration", "s3:PutMetricsConfiguration", "s3:PutObject", "s3:DeleteAccessPoint"], "Resource": "*"}, {"Sid": "AllowPolicyRds", "Effect": "Allow", "Action": ["rds:CreateDBInstance", "rds:ModifyDBInstance", "rds:DeleteDBInstance", "rds:CreateDBSubnetGroup", "rds:CreateDBParameterGroup", "rds:CreateDBClusterParameterGroup", "rds:CreateDBCluster", "rds:CreateDBClusterSnapshot", "rds:CreateDBSnapshot", "rds:ModifyDBClusterParameterGroup", "rds:DeleteDBClusterParameterGroup", "rds:DeleteDBParameterGroup", "rds:DeleteDBCluster", "rds:DeleteDBClusterSnapshot", "rds:DeleteDBSnapshot", "rds:DeleteDBSubnetGroup", "rds:RebootDBInstance", "rds:CreateDBProxy", "rds:CreateDBProxyEndpoint", "rds:ModifyDBCluster", "rds:ModifyDBParameterGroup", "rds:ModifyDBSubnetGroup", "rds:ModifyDBProxy", "rds:ModifyDBProxyEndpoint", "rds:ModifyDBProxyTargetGroup", "rds:DeleteDBProxy", "rds:DeleteDBProxyEndpoint", "rds:RegisterDBProxyTargets", "rds:DeregisterDBProxyTargets", "rds:AddTagsToResource", "rds:RemoveTagsFromResource"], "Resource": "*"}]}