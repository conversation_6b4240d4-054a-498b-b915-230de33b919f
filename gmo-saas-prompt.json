Please refactor the following AWS IAM policy files to replace wildcard resources with specific ARNs:

Files to modify:
- gmo-saas-policy1.json
- gmo-saas-policy2.json
- gmo-saas-policy3.json

**Task Requirements:**
1. For each Statement in these policy files, replace `"Resource": "*"` with specific ARNs appropriate for each AWS service
2. Use the provided AWS account information to construct proper ARNs
3. Follow AWS IAM best practices by applying the principle of least privilege - grant access only to the specific resources needed
4. Ensure ARNs are correctly formatted for each service type (e.g., S3 buckets, Lambda functions, RDS instances, etc.)

**AWS Account Context:**
- Account ID: ************
- Environment: saas-dev (development environment)
- Access via AWS SSO with AuctionSaaS-DeveloperAccess-OEC role

**Expected Outcome:**
- All wildcard resources (`"Resource": "*"`) should be replaced with specific, scoped ARNs
- ARNs should follow the format: `arn:aws:service:region: ************:resource-type/resource-name`
- Maintain the same permissions scope but with explicit resource targeting
- Preserve all other policy elements (Effect, Action, Condition, etc.) unchanged

Please analyze each policy file's current permissions and suggest appropriate specific ARNs based on the services and actions defined in each statement.
