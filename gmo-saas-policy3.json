{"Version": "2012-10-17", "Statement": [{"Sid": "AllowPolicyCloudFront", "Effect": "Allow", "Action": ["cloudwatch:TagResource", "cloudwatch:UntagResource", "cloudfront:CreateDistribution", "cloudfront:UpdateDistribution", "cloudfront:DeleteDistribution", "cloudfront:CreateInvalidation", "cloudfront:CreateCloudFrontOriginAccessIdentity", "cloudfront:UpdateCloudFrontOriginAccessIdentity", "cloudfront:DeleteCloudFrontOriginAccessIdentity", "cloudfront:CreateFunction", "cloudfront:UpdateFunction", "cloudfront:DeleteFunction", "cloudfront:CreateCachePolicy", "cloudfront:UpdateCachePolicy", "cloudfront:DeleteCachePolicy", "cloudfront:CreateOriginRequestPolicy", "cloudfront:UpdateOriginRequestPolicy", "cloudfront:DeleteOriginRequestPolicy", "cloudfront:CreateResponseHeadersPolicy", "cloudfront:UpdateResponseHeadersPolicy", "cloudfront:DeleteResponseHeadersPolicy", "cloudfront:TagResource", "cloudfront:UntagResource", "cloudfront:AssociateAlias", "cloudfront:DisassociateAlias", "cloudwatch:PutMetricAlarm", "cloudwatch:DeleteAlarms", "cloudwatch:PutMetricStream", "cloudwatch:PutMetricData"], "Resource": "*"}, {"Sid": "AllowPolicySecretsmanager", "Effect": "Allow", "Action": ["secretsmanager:<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretsmanager:PutSecretV<PERSON>ue", "secretsmanager:UpdateSecret", "secretsmanager:TagResource", "secretsmanager:DeleteSecret", "secretsmanager:UntagResource"], "Resource": "*"}, {"Sid": "AllowPolicyAcm", "Effect": "Allow", "Action": ["acm:RequestCertificate", "acm:AddTagsToCertificate", "acm:DeleteCertificate", "acm:ResendValidationEmail", "acm:ImportCertificate", "acm:ExportCertificate"], "Resource": "*"}, {"Sid": "AllowPolicyRoute53", "Effect": "Allow", "Action": ["route53:ChangeResourceRecordSets", "route53:DeleteHostedZone", "route53:CreateHostedZone"], "Resource": "*"}, {"Sid": "AllowPolicyLogs", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "logs:DeleteLogGroup", "logs:DeleteLogStream", "logs:PutResourcePolicy", "logs:CreateLogDelivery", "logs:CreateDelivery", "logs:PutDestinationPolicy", "logs:PutDestination", "logs:PutRetentionPolicy", "logs:DeleteRetentionPolicy", "logs:TagLogGroup", "logs:UntagLogGroup"], "Resource": "*"}, {"Sid": "AllowPolicyEvents", "Effect": "Allow", "Action": ["events:PutRule", "events:PutTargets", "events:RemoveTargets", "events:DeleteRule", "events:PutEvents", "events:EnableRule", "events:DisableRule", "events:TagResource", "events:UntagResource"], "Resource": "*"}, {"Sid": "AllowPolicySns", "Effect": "Allow", "Action": ["sns:CreateTopic", "sns:SetTopicAttributes", "sns:DeleteTopic", "sns:Subscribe", "sns:Unsubscribe", "sns:Publish", "sns:TagResource", "sns:UntagResource"], "Resource": "*"}]}